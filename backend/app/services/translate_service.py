import logging
from typing import List
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.llms import LLMType, get_llm

logger = logging.getLogger(__name__)


class TranslateService:

    def __init__(self):
        pass

    def translate_language_code_list(
        self, language_info_list: List[str], target_language: str = "zh-CN"
    ) -> str:
        """
        翻译语言标识

        Args:
            language_code: 要翻译的语言代码，如 'en-US', 'zh-CN'
            target_language: 目标语言，默认为 'zh-CN'

        Returns:
            翻译后的语言名称
        """

        logger.info(language_info_list)
        # 将语言代码列表转换为字符串
        language_codes_text = ",".join([info["code"] for info in language_info_list])
        prompt_template = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """
你是一位语言专家。你会将用给到的地区语言code转为对应的中文语言标识，并且你会按照一定格式进行输出。比如：
用户输入：zh-CN,zh-TW,en-US
你会返回：{
    "zh-CN": "简体中文",
    "zh-TW": "繁体中文",
    "en-US": "英语
}
""",
                ),
                ("user", "{language_codes_text}"),
            ]
        )

        parser = StrOutputParser()

        llm = get_llm(llm_type=LLMType.TONGYI)

        chain = prompt_template | llm | parser

        result = chain.invoke()

        logger.info(result)

        return result

    def translate_content_list(self, info: any):
        # 先翻译target_language_list
        result = self.translate_language_code_list(info["target_language_list"])
        # 在翻译list
        pass

    def translate_content(self, key: str, target_language: str = "zh-CN") -> str:
        """
        翻译具体的内容key

        Args:
            key: 要翻译的内容key
            target_language: 目标语言，默认为 'zh-CN'

        Returns:
            翻译后的内容
        """
        if target_language not in self.content_translations:
            return key  # 如果目标语言不支持，返回原始key

        content_map = self.content_translations[target_language]
        return content_map.get(key, key)

    def get_supported_languages(self) -> list:
        """
        获取支持的语言列表

        Returns:
            支持的语言代码列表
        """
        return list(self.language_names.keys())

    def get_available_content_keys(self, language: str = "zh-CN") -> list:
        """
        获取指定语言的可用内容key列表

        Args:
            language: 语言代码

        Returns:
            可用的内容key列表
        """
        if language not in self.content_translations:
            return []
        return list(self.content_translations[language].keys())
