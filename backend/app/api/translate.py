"""
翻译API接口层 - 简化版本，只保留上传接口
"""

# 保存上传的文件到临时目录
import tempfile
import os
from flask import Blueprint, request, jsonify
import logging
from app.services.excel_service import ExcelService
from app.services.translate_service import TranslateService

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
translate_bp = Blueprint("translate", __name__)


@translate_bp.route("/translate/upload", methods=["POST"])
def upload_excel():
    """Excel文件上传解析接口 - 返回demo数据"""
    try:
        # 检查是否有文件上传
        if "file" not in request.files:
            return jsonify({"code": -1, "message": "没有上传文件"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"code": -1, "message": "文件名为空"}), 400

        excel_service = ExcelService()
        translate_service = TranslateService()

        print("excel_service", excel_service)

        file_path = excel_service.create_temp_file(file)
        print("file_path", file_path)

        # 解析Excel文件
        parse_result = excel_service.parse_excel_to_translation_tasks(file_path)

        # 翻译内容
        result = translate_service.translate_content_list(parse_result)

        # 删除临时文件
        os.remove(file_path)

        return jsonify({"code": 0, "message": "", "data": parse_result})

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500


@translate_bp.route("/translate", methods=["POST"])
def batch_translate():
    """批量翻译接口 - 返回demo响应"""
    try:
        # 返回demo响应
        demo_response = {
            "code": 0,
            "message": "翻译请求已接收",
            "data": {
                "task_id": "demo_task_123",
                "status": "processing",
                "message": "翻译任务已开始处理",
            },
        }

        return jsonify(demo_response)

    except Exception as e:
        logger.error(f"批量翻译处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500
